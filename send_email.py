#!/usr/bin/env python3
"""
Email notification script for Lenskart Sanity Test Results
Sends rich HTML email with test reports attached
"""

import smtplib
import os
import sys
import zipfile
import tempfile
import shutil
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
import re
from pathlib import Path
import argparse

class SanityEmailSender:
    def __init__(self):
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587
        self.sender_email = "<EMAIL>"
        self.sender_password = "cbae kdat vpjd nchc"  # App password from environment
        
        # Email recipients (same as Jenkinsfile)
        self.recipients = [
            '<EMAIL>',
            '<EMAIL>', 
            '<EMAIL>',
            '<EMAIL>',
            's.ma<PERSON><EMAIL>',
            '<PERSON><PERSON>.<PERSON><EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]

    def parse_test_results(self, reports_dir="reports"):
        """Parse test results from reports/index.html"""
        index_file = os.path.join(reports_dir, "index.html")
        
        if not os.path.exists(index_file):
            return {
                'total': 0, 'passed': 0, 'failed': 0, 'skipped': 0,
                'success_rate': 0, 'execution_time': 'Unknown',
                'region': 'Unknown', 'source': 'Unknown'
            }
        
        try:
            with open(index_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract test statistics using regex
            total_match = re.search(r'<div class="stat-card total">.*?<h4>(\d+)</h4>', content, re.DOTALL)
            passed_match = re.search(r'<div class="stat-card passed">.*?<h4>(\d+)</h4>', content, re.DOTALL)
            failed_match = re.search(r'<div class="stat-card failed">.*?<h4>(\d+)</h4>', content, re.DOTALL)
            success_rate_match = re.search(r'<div class="stat-card">.*?<h4>(\d+)%</h4>', content, re.DOTALL)
            
            # Extract execution time and region
            exec_time_match = re.search(r'Execution Time:</strong> ([^<]+)', content)
            region_match = re.search(r'Region Filter:</strong> ([^<]+)', content)
            source_match = re.search(r'Source:</strong> ([^<]+)', content)
            
            total = int(total_match.group(1)) if total_match else 0
            passed = int(passed_match.group(1)) if passed_match else 0
            failed = int(failed_match.group(1)) if failed_match else 0
            skipped = max(0, total - passed - failed)  # Calculate skipped
            
            return {
                'total': total,
                'passed': passed,
                'failed': failed,
                'skipped': skipped,
                'success_rate': int(success_rate_match.group(1)) if success_rate_match else 0,
                'execution_time': exec_time_match.group(1).strip() if exec_time_match else 'Unknown',
                'region': region_match.group(1).strip() if region_match else 'All Regions',
                'source': source_match.group(1).strip() if source_match else 'sanity/testcases.txt'
            }
            
        except Exception as e:
            print(f"Error parsing test results: {e}")
            return {
                'total': 0, 'passed': 0, 'failed': 0, 'skipped': 0,
                'success_rate': 0, 'execution_time': 'Unknown',
                'region': 'Unknown', 'source': 'Unknown'
            }

    def create_reports_zip(self, reports_dir="reports"):
        """Create a ZIP file of the reports directory"""
        if not os.path.exists(reports_dir):
            return None
            
        # Create temporary ZIP file
        temp_dir = tempfile.mkdtemp()
        zip_path = os.path.join(temp_dir, "sanity_test_reports.zip")
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(reports_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, os.path.dirname(reports_dir))
                        zipf.write(file_path, arcname)
            
            return zip_path
        except Exception as e:
            print(f"Error creating ZIP file: {e}")
            return None

    def get_region_info(self, region):
        """Get region-specific information"""
        region_info = {
            'IN': {'flag': '🇮🇳', 'name': 'India', 'currency': 'INR', 'features': 'India-specific features'},
            'SG': {'flag': '🇸🇬', 'name': 'Singapore', 'currency': 'SGD', 'features': 'Singapore-specific features'},
            'UAE': {'flag': '🇦🇪', 'name': 'United Arab Emirates', 'currency': 'AED', 'features': 'UAE-specific features'},
            'SA': {'flag': '🇸🇦', 'name': 'Saudi Arabia', 'currency': 'SAR', 'features': 'Saudi-specific features'},
            'TH': {'flag': '🇹🇭', 'name': 'Thailand', 'currency': 'THB', 'features': 'Thailand-specific features'}
        }
        
        if region in region_info:
            info = region_info[region]
            return f"{info['flag']} {region} ({info['name']}): Tests for {info['name']} market with {info['currency']} currency and {info['features']}"
        else:
            return f"🌍 {region}: Multi-region test execution"

    def create_email_content(self, test_results, region_filter=None):
        """Create rich HTML email content similar to Jenkinsfile"""
        
        # Determine status and colors
        if test_results['failed'] > 0:
            status_icon = "❌"
            status_color = "#dc3545"
            build_status = "UNSTABLE"
        elif test_results['total'] == 0:
            status_icon = "⚠️"
            status_color = "#ffc107"
            build_status = "NO TESTS"
        else:
            status_icon = "✅"
            status_color = "#28a745"
            build_status = "SUCCESS"
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Get region information
        region_display = region_filter if region_filter else "All Regions"
        region_info_text = self.get_region_info(region_display)
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 800px; margin: 0 auto; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ background-color: {status_color}; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }}
        .content {{ padding: 20px; }}
        .section {{ margin-bottom: 20px; }}
        .section h3 {{ color: #333; border-bottom: 2px solid #eee; padding-bottom: 5px; }}
        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 15px 0; }}
        .stat-card {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; border-left: 4px solid {status_color}; }}
        .stat-number {{ font-size: 24px; font-weight: bold; color: {status_color}; }}
        .stat-label {{ font-size: 12px; color: #666; text-transform: uppercase; }}
        .info-table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        .info-table th, .info-table td {{ padding: 8px 12px; text-align: left; border-bottom: 1px solid #eee; }}
        .info-table th {{ background-color: #f8f9fa; font-weight: bold; }}
        .footer {{ background-color: #f8f9fa; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 12px; color: #666; }}
        .success {{ color: #28a745; }}
        .failure {{ color: #dc3545; }}
        .warning {{ color: #ffc107; }}
        .region-info {{ background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{status_icon} Android Prod Sanity Test Execution Report</h1>
            <h2>Lenskart App Automation</h2>
            <p>Status: <strong>{build_status}</strong></p>
        </div>

        <div class="content">
            <div class="section">
                <h3>📊 Test Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">{test_results['total']}</div>
                        <div class="stat-label">Total Tests</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number success">{test_results['passed']}</div>
                        <div class="stat-label">Passed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number failure">{test_results['failed']}</div>
                        <div class="stat-label">Failed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number warning">{test_results['skipped']}</div>
                        <div class="stat-label">Skipped</div>
                    </div>
                </div>
            </div>

            <div class="region-info">
                <h3>🌍 Region Information</h3>
                <p><strong>{region_info_text}</strong></p>
            </div>

            <div class="section">
                <h3>🔧 Execution Information</h3>
                <table class="info-table">
                    <tr><th>Region Filter</th><td>{region_display}</td></tr>
                    <tr><th>Test Source</th><td>{test_results['source']}</td></tr>
                    <tr><th>Framework</th><td>Android Mobile Testing Framework</td></tr>
                    <tr><th>Execution Start Time</th><td>{test_results['execution_time']}</td></tr>
                    <tr><th>Email Trigger Time</th><td>{timestamp}</td></tr>
                    <tr><th>Environment</th><td>Local Trigger</td></tr>
                </table>
            </div>

            <div class="section">
                <h3>📈 Test Results Summary</h3>
                <p><strong>Pass Rate:</strong> {test_results['success_rate']}%</p>
                <p><strong>Failure Rate:</strong> {100 - test_results['success_rate']}%</p>
                {f"<p style='color: #dc3545;'><strong>⚠️ {test_results['failed']} test(s) failed. Please review the detailed report.</strong></p>" if test_results['failed'] > 0 else ""}
                {f"<p style='color: #ffc107;'><strong>ℹ️ {test_results['skipped']} test(s) were skipped.</strong></p>" if test_results['skipped'] > 0 else ""}
            </div>

            <div class="section">
                <h3>📋 Test Reports & Attachments</h3>
                <p><strong>📊 Complete Test Reports:</strong> Detailed HTML reports with screenshots and execution logs</p>
                <p><strong>📸 Screenshots:</strong> Failure screenshots automatically captured for debugging</p>
                <p><strong>📝 Execution Logs:</strong> Complete test execution logs with detailed information</p>
                <p style="margin-top: 15px;"><em>All reports and logs are attached as a ZIP file to this email.</em></p>
            </div>

            <div class="section">
                <h3>📝 Additional Information</h3>
                <ul>
                    <li><strong>Test Framework:</strong> Android Mobile Testing Framework</li>
                    <li><strong>Execution Mode:</strong> Automated Sanity Testing</li>
                    <li><strong>Report Format:</strong> HTML with Interactive Dashboard</li>
                    <li><strong>Screenshot Capture:</strong> Automatic on Test Failures</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>This is an automated message from Lenskart App Automation Framework</p>
            <p>Android Prod Sanity Testing Report | Generated on {timestamp}</p>
        </div>
    </div>
</body>
</html>
"""
        return html_content

    def send_email(self, region_filter=None, reports_dir="reports"):
        """Send email with test results and reports attachment"""

        if not self.sender_password:
            print("❌ Error: GMAIL_APP_PASSWORD environment variable not set")
            print("Please set your Gmail app password: export GMAIL_APP_PASSWORD='your_app_password'")
            return False

        try:
            # Parse test results
            test_results = self.parse_test_results(reports_dir)

            # Create ZIP file of reports
            zip_path = self.create_reports_zip(reports_dir)

            # Create email message
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = ', '.join(self.recipients)

            # Determine subject based on results
            if test_results['failed'] > 0:
                status_icon = "❌"
                status = "UNSTABLE"
            elif test_results['total'] == 0:
                status_icon = "⚠️"
                status = "NO TESTS"
            else:
                status_icon = "✅"
                status = "SUCCESS"

            region_text = f" - {region_filter}" if region_filter else ""
            msg['Subject'] = f"{status_icon} {status}: Prod Android : Lenskart Sanity Tests{region_text}"

            # Create and attach HTML content
            html_content = self.create_email_content(test_results, region_filter)
            msg.attach(MIMEText(html_content, 'html'))

            # Attach ZIP file if created successfully
            if zip_path and os.path.exists(zip_path):
                with open(zip_path, "rb") as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())

                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= "sanity_test_reports_{datetime.now().strftime("%Y%m%d_%H%M%S")}.zip"'
                )
                msg.attach(part)
                print(f"✅ Reports ZIP file attached: {os.path.basename(zip_path)}")
            else:
                print("⚠️ Warning: Could not create reports ZIP file")

            # Send email
            print(f"📧 Sending email to {len(self.recipients)} recipients...")

            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender_email, self.sender_password)

            text = msg.as_string()
            server.sendmail(self.sender_email, self.recipients, text)
            server.quit()

            print("✅ Email sent successfully!")
            print(f"📊 Test Summary: {test_results['passed']}/{test_results['total']} passed ({test_results['success_rate']}%)")

            # Clean up temporary ZIP file
            if zip_path and os.path.exists(zip_path):
                try:
                    os.remove(zip_path)
                    shutil.rmtree(os.path.dirname(zip_path))
                except:
                    pass

            return True

        except Exception as e:
            print(f"❌ Error sending email: {e}")
            return False


def main():
    """Main function to handle command line arguments"""
    parser = argparse.ArgumentParser(description='Send Lenskart Sanity Test Results via Email')
    parser.add_argument('--region', '-r', help='Region filter (IN, SG, UAE, SA, TH)', default=None)
    parser.add_argument('--reports-dir', '-d', help='Reports directory path', default='reports')

    args = parser.parse_args()

    print("📧 Lenskart Sanity Test Email Notification")
    print("=" * 50)

    # Check if reports directory exists
    if not os.path.exists(args.reports_dir):
        print(f"❌ Error: Reports directory '{args.reports_dir}' not found")
        sys.exit(1)

    # Create email sender and send email
    email_sender = SanityEmailSender()
    success = email_sender.send_email(args.region, args.reports_dir)

    if success:
        print("🎉 Email notification completed successfully!")
        sys.exit(0)
    else:
        print("💥 Email notification failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
