# Maestro TCP Error - Quick Reference

## Immediate Fix Commands:
fix-maestro                    # Quick fix alias
./fix-maestro-connection.sh    # Enhanced fix script
reset-automation               # Complete reset alias

## Monitoring Commands:
check-device                   # Check connected devices
check-port                     # Check port 7001 usage
./maestro-health-monitor.sh --monitor  # Health check

## Prevention:
./maestro-health-monitor.sh --watch    # Continuous monitoring
./maestro-health-monitor.sh --auto-fix # Auto-fix issues

## Daily Maintenance:
- Cron job runs daily at 9 AM
- Automatically fixes common issues
- Logs all activities

## Manual Commands:
pkill -f maestro && pkill -f adb && adb kill-server && sleep 3 && adb start-server && adb devices
