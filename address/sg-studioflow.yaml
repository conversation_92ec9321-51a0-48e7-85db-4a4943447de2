appId: com.lenskart.app

env:

  EMAIL_ID: <EMAIL>
  LAST_NAME: Test
---

# Wait for login to complete
- waitForAnimationToEnd

#On store page, verifying select a store text
- tapOn:
    id: "com.lenskart.app.store:id/tv_toolbar_title"

# Selecting the second store address
- tapOn:
    id: "com.lenskart.app.store:id/proceed_icon"
    index: 2

# Verifying Contact details text
- tapOn:
    id: "com.lenskart.app.store:id/tv_toolbar_title"

#verifying selected store
- tapOn:
    id: "com.lenskart.app.store:id/marker_pin"

#all data like name,phone no are autofield

- tapOn: "Last Name*"
- inputText: ${LAST_NAME}

  #Tapping on Email id
#- tapOn:
  # id: "com.lenskart.app.store:id/edt_input_text"
  # index: 3

#Entering input in Email id
#- inputText: ${EMAIL_ID}

#tapping on Confirm detail button
- tapOn: "Confirm Details"