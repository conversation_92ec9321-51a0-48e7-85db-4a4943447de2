# Maestro Automation Aliases
# Add these to your ~/.zshrc or ~/.bash_profile for permanent access

# Quick fix for TCP connection errors
alias fix-maestro="pkill -f maestro && pkill -f adb && adb kill-server && sleep 3 && adb start-server && adb devices"

# Enhanced fix using the script
alias fix-maestro-enhanced="./fix-maestro-connection.sh"

# Quick device check
alias check-device="adb devices"

# Quick port check
alias check-port="lsof -i :7001"

# Kill all Maestro processes
alias kill-maestro="pkill -f maestro"

# Reset ADB only
alias reset-adb="adb kill-server && sleep 2 && adb start-server"

# Complete system reset for automation
alias reset-automation="pkill -f maestro && pkill -f adb && adb kill-server && sleep 3 && adb start-server && adb devices"

# Quick test runner with error handling
alias run-maestro="fix-maestro && maestro test"

echo "Maestro aliases loaded! Available commands:"
echo "  fix-maestro          - Quick TCP error fix"
echo "  fix-maestro-enhanced - Enhanced fix with verification"
echo "  check-device         - Check connected devices"
echo "  check-port           - Check port 7001 usage"
echo "  kill-maestro         - Kill Maestro processes"
echo "  reset-adb            - Reset ADB server"
echo "  reset-automation     - Complete automation reset"
echo "  run-maestro          - Fix and run Maestro test"
