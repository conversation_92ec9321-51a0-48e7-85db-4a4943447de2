#!/bin/bash

#===============================================================================
# Email Setup Script for Lenskart Sanity Test Notifications
# Sets up Gmail SMTP configuration and tests email functionality
#===============================================================================

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}📧 Lenskart Sanity Test Email Setup${NC}"
echo -e "${BLUE}═══════════════════════════════════════${NC}"
echo ""

# Check if Python 3 is installed
echo -e "${BLUE}🔍 Checking Python 3 installation...${NC}"
if command -v python3 &> /dev/null; then
    python_version=$(python3 --version)
    echo -e "${GREEN}✅ Python 3 found: ${python_version}${NC}"
else
    echo -e "${RED}❌ Python 3 not found${NC}"
    echo -e "${YELLOW}Please install Python 3 to use email notifications${NC}"
    exit 1
fi

echo ""

# Check if send_email.py exists
echo -e "${BLUE}🔍 Checking email script...${NC}"
if [ -f "send_email.py" ]; then
    echo -e "${GREEN}✅ Email script found: send_email.py${NC}"
else
    echo -e "${RED}❌ Email script not found: send_email.py${NC}"
    echo -e "${YELLOW}Please ensure send_email.py is in the current directory${NC}"
    exit 1
fi

echo ""

# Check current Gmail app password setting
echo -e "${BLUE}🔍 Checking Gmail app password configuration...${NC}"
if [ -n "$GMAIL_APP_PASSWORD" ]; then
    echo -e "${GREEN}✅ GMAIL_APP_PASSWORD environment variable is set${NC}"
    echo -e "${YELLOW}Current value: ${GMAIL_APP_PASSWORD:0:4}****${NC}"
else
    echo -e "${YELLOW}⚠️ GMAIL_APP_PASSWORD environment variable not set${NC}"
    echo ""
    echo -e "${BLUE}📋 To set up Gmail app password:${NC}"
    echo -e "${YELLOW}1. Go to your Google Account settings${NC}"
    echo -e "${YELLOW}2. Navigate to Security > 2-Step Verification${NC}"
    echo -e "${YELLOW}3. Generate an App Password for 'Mail'${NC}"
    echo -e "${YELLOW}4. Set the environment variable:${NC}"
    echo -e "${YELLOW}   export GMAIL_APP_PASSWORD='your_16_character_app_password'${NC}"
    echo ""
    
    read -p "Do you want to set the GMAIL_APP_PASSWORD now? (y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}Enter your Gmail app password (16 characters):${NC}"
        read -s gmail_password
        
        if [ ${#gmail_password} -eq 16 ]; then
            export GMAIL_APP_PASSWORD="$gmail_password"
            echo -e "${GREEN}✅ GMAIL_APP_PASSWORD set for this session${NC}"
            echo -e "${YELLOW}To make it permanent, add this to your ~/.bashrc or ~/.zshrc:${NC}"
            echo -e "${YELLOW}export GMAIL_APP_PASSWORD='$gmail_password'${NC}"
        else
            echo -e "${RED}❌ Invalid app password length. Gmail app passwords are exactly 16 characters.${NC}"
            exit 1
        fi
    else
        echo -e "${YELLOW}⚠️ Email notifications will not work without GMAIL_APP_PASSWORD${NC}"
        exit 1
    fi
fi

echo ""

# Test email functionality
echo -e "${BLUE}🧪 Testing email functionality...${NC}"
echo -e "${YELLOW}This will send a test email to verify the configuration${NC}"

read -p "Do you want to send a test email? (y/n): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Create a test reports directory if it doesn't exist
    if [ ! -d "reports" ]; then
        echo -e "${BLUE}📁 Creating test reports directory...${NC}"
        mkdir -p reports
        
        # Create a simple test index.html
        cat > reports/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Test Results - Email Setup Test</title>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Email Setup Test Results</h1>
            <p><strong>📅 Execution Time:</strong> Email Setup Test</p>
            <p><strong>📋 Source:</strong> setup_email.sh</p>
            <p><strong>🌍 Region Filter:</strong> TEST</p>
        </div>
        <div class="summary">
            <h3>📊 Execution Summary</h3>
            <div class="stats">
                <div class="stat-card total">
                    <h4>1</h4>
                    <p>Total Tests</p>
                </div>
                <div class="stat-card passed">
                    <h4>1</h4>
                    <p>Passed</p>
                </div>
                <div class="stat-card failed">
                    <h4>0</h4>
                    <p>Failed</p>
                </div>
                <div class="stat-card">
                    <h4>100%</h4>
                    <p>Success Rate</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
EOF
        echo -e "${GREEN}✅ Test reports created${NC}"
    fi
    
    # Send test email
    echo -e "${BLUE}📧 Sending test email...${NC}"
    python3 ./send_email.py --region "TEST" --reports-dir "reports"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Test email sent successfully!${NC}"
        echo -e "${GREEN}📧 Check your inbox for the test email${NC}"
    else
        echo -e "${RED}❌ Test email failed${NC}"
        echo -e "${YELLOW}Please check your Gmail app password and network connection${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ Test email skipped${NC}"
fi

echo ""
echo -e "${BLUE}📋 Email Setup Summary:${NC}"
echo -e "${GREEN}✅ Python 3: Available${NC}"
echo -e "${GREEN}✅ Email Script: Available${NC}"

if [ -n "$GMAIL_APP_PASSWORD" ]; then
    echo -e "${GREEN}✅ Gmail App Password: Configured${NC}"
else
    echo -e "${RED}❌ Gmail App Password: Not configured${NC}"
fi

echo ""
echo -e "${BLUE}📧 Email Recipients:${NC}"
echo -e "${YELLOW}The following recipients will receive sanity test notifications:${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"
echo -e "${YELLOW}- <EMAIL>${NC}"

echo ""
echo -e "${GREEN}🎉 Email setup completed!${NC}"
echo -e "${BLUE}Now you can run sanity tests with optional email notifications:${NC}"
echo ""
echo -e "${GREEN}📧 WITH Email Notifications:${NC}"
echo -e "${YELLOW}  ./sanity.sh IN sendMail=true     # Send email after IN tests${NC}"
echo -e "${YELLOW}  ./sanity.sh SG sendMail=true     # Send email after SG tests${NC}"
echo -e "${YELLOW}  ./sanity.sh UAE sendMail=true    # Send email after UAE tests${NC}"
echo -e "${YELLOW}  ./sanity.sh SA sendMail=true     # Send email after SA tests${NC}"
echo -e "${YELLOW}  ./sanity.sh TH sendMail=true     # Send email after TH tests${NC}"
echo -e "${YELLOW}  ./sanity.sh sendMail=true        # Send email after all tests${NC}"
echo ""
echo -e "${BLUE}🚫 WITHOUT Email Notifications (default):${NC}"
echo -e "${YELLOW}  ./sanity.sh IN                   # Run IN tests only${NC}"
echo -e "${YELLOW}  ./sanity.sh SG                   # Run SG tests only${NC}"
echo -e "${YELLOW}  ./sanity.sh                      # Run all tests only${NC}"
