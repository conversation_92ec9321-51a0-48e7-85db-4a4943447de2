#!/bin/bash

#===============================================================================
# Lenskart Sanity Test Suite with Region Support
# Same reporting format as run_sanity_test.sh with region filtering
#
# Usage:
#   ./sanity.sh                    # Run all international countries (TH,SA,UAE,SG) - excludes IN
#   ./sanity.sh IN                 # Run only IN tests from testcases.txt
#   ./sanity.sh SG                 # Run only SG tests from testcases.txt
#   ./sanity.sh UAE                # Run only UAE tests from testcases.txt
#   ./sanity.sh SA                 # Run only SA tests from testcases.txt
#   ./sanity.sh TH                 # Run only TH tests from testcases.txt
#   ./sanity.sh SG_TH              # Run SG and TH tests together
#   ./sanity.sh UAE_SA             # Run UAE and SA tests together
#   ./sanity.sh IN sendMail=true   # Run IN tests and send email notification
#   ./sanity.sh SG_TH sendMail=true # Run SG & TH tests and send email notification
#   ./sanity.sh UAE_SA sendMail=true # Run UAE & SA tests and send email notification
#   ./sanity.sh sendMail=true      # Run all international countries and send email notification
#===============================================================================

set +e  # Continue execution even if tests fail

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
DATETIME=$(date '+%Y-%m-%d %H:%M:%S')
REPORT_DIR="reports"
TESTCASES_FILE="sanity/testcases.txt"

# Parse command line arguments
REGION_FILTER=""
SEND_MAIL=false

# Parse arguments
for arg in "$@"; do
    if [[ "$arg" == sendMail=true ]]; then
        SEND_MAIL=true
    elif [[ "$arg" =~ ^(IN|SG|UAE|SA|TH)$ ]]; then
        REGION_FILTER="$arg"
    elif [[ "$arg" == "SG_TH" ]]; then
        REGION_FILTER="SG_TH"
    elif [[ "$arg" == "UAE_SA" ]]; then
        REGION_FILTER="UAE_SA"
    fi
done

# Test tracking
TOTAL=0
PASSED=0
FAILED=0
declare -a TEST_RESULTS=()

echo -e "${BLUE}🧪 Sanity Test Results${NC}"
echo -e "${YELLOW}📅 ${DATETIME}${NC}"
echo -e "${YELLOW}📁 Reports: ${REPORT_DIR}${NC}"
if [ -n "$REGION_FILTER" ]; then
    echo -e "${YELLOW}🌍 Region Filter: ${REGION_FILTER}${NC}"
fi
if [ "$SEND_MAIL" = true ]; then
    echo -e "${YELLOW}📧 Email Notification: Enabled${NC}"
else
    echo -e "${YELLOW}📧 Email Notification: Disabled${NC}"
fi
echo ""

# Clean up reports directory
cleanup_reports() {
    echo -e "${BLUE}🧹 Cleaning reports directory...${NC}"
    rm -rf "$REPORT_DIR"
    mkdir -p "$REPORT_DIR"
    echo -e "${GREEN}✅ Reports directory ready${NC}"
    echo ""
}

# Function to determine region from filename or path with line context
get_region() {
    local file_path="$1"
    local line_number="$2"
    local filename=$(basename "$file_path")

    # Handle VPN files
    if [[ "$filename" == "sg-vpn.yaml" ]]; then
        echo "SG"
    elif [[ "$filename" == "uae-vpn.yaml" ]]; then
        echo "UAE"
    elif [[ "$filename" == "sa-vpn.yaml" ]]; then
        echo "SA"
    elif [[ "$filename" == "th-vpn.yaml" ]]; then
        echo "TH"
    elif [[ "$filename" == "disconnect-vpn.yaml" ]]; then
        # Determine region based on line context in testcases.txt
        if [ "$line_number" -eq 35 ]; then
            echo "SA"  # SA section disconnect-vpn
        elif [ "$line_number" -eq 47 ]; then
            echo "TH"  # TH section disconnect-vpn
        else
            echo "DISCONNECT_VPN"  # Fallback
        fi
    elif [[ "$filename" == "sg-"* ]]; then
        echo "SG"
    elif [[ "$filename" == "uae-"* ]]; then
        echo "UAE"
    elif [[ "$filename" == "sa-"* ]]; then
        echo "SA"
    elif [[ "$filename" == "th-"* ]]; then
        echo "TH"
    elif [[ "$filename" == "in-"* ]]; then
        echo "IN"
    elif [[ "$file_path" == *"/in/"* ]]; then
        echo "IN"
    elif [[ "$file_path" == *"/sg/"* ]]; then
        echo "SG"
    elif [[ "$file_path" == *"/uae/"* ]]; then
        echo "UAE"
    elif [[ "$file_path" == *"/sa/"* ]]; then
        echo "SA"
    elif [[ "$file_path" == *"/th/"* ]]; then
        echo "TH"
    else
        echo "IN"  # Default to IN
    fi
}

# Function to get region flag
get_region_flag() {
    local region="$1"
    if [ "$region" = "SG" ]; then
        echo "🇸🇬"
    elif [ "$region" = "UAE" ]; then
        echo "🇦🇪"
    elif [ "$region" = "SA" ]; then
        echo "🇸🇦"
    elif [ "$region" = "TH" ]; then
        echo "🇹🇭"
    elif [ "$region" = "DISCONNECT_VPN" ]; then
        echo "🔌"
    else
        echo "🇮🇳"
    fi
}

# Run individual test with enhanced result detection
run_test() {
    local file="$1"
    local num="$2"
    local region="$3"
    local name=$(basename "$file" .yaml | sed 's/[^a-zA-Z0-9]/_/g')
    local region_flag=$(get_region_flag "$region")
    
    ((TOTAL++))
    echo -e "${BLUE}🔄 Test ${num}: [${region_flag} ${region}] ${name}${NC}"
    echo -e "${YELLOW}   📄 ${file}${NC}"
    echo -e "${YELLOW}   ⏰ Started: $(date '+%H:%M:%S')${NC}"
    
    # Check if file exists
    if [ ! -f "$file" ]; then
        echo -e "${RED}❌ FAILED - File not found${NC}"
        ((FAILED++))
        TEST_RESULTS+=("FAILED:$name:$num:File not found")
        echo ""
        return 1
    fi
    
    # Create test output directory
    local output_dir="${REPORT_DIR}/test_${num}_${name}"
    local screenshots_dir="${output_dir}/screenshots"
    mkdir -p "$output_dir"
    mkdir -p "$screenshots_dir"
    
    # Run maestro test with improved result detection
    echo -e "${YELLOW}   ⏳ Running test...${NC}"
    
    local maestro_output
    local exit_code
    
    maestro_output=$(maestro test "$file" \
        --format html \
        --output "$output_dir/report.html" 2>&1)
    exit_code=$?
    
    # Clean output and save to log
    local clean_output=""
    local in_stack_trace=false
    
    while IFS= read -r line; do
        if [[ "$line" =~ "The stack trace was:" ]]; then
            in_stack_trace=true
        elif [[ "$line" =~ "Flow Passed" ]] || [[ "$line" =~ "Passed]" ]]; then
            clean_output+="$line"$'\n'
            in_stack_trace=false
        elif [[ "$in_stack_trace" == false ]]; then
            clean_output+="$line"$'\n'
        fi
    done <<< "$maestro_output"
    
    echo "$clean_output" > "$output_dir/execution.log"
    
    # Enhanced result detection
    local test_passed=false
    
    # Check for critical failures first
    if echo "$clean_output" | grep -qi -E "(invalid.*command.*format|yaml.*error|syntax.*error)" || \
       echo "$clean_output" | grep -qi -E "(unable.*to.*launch|app.*not.*found|device.*not.*connected)"; then
        test_passed=false
        echo -e "${YELLOW}   ❌ Critical error detected${NC}"
    elif [ $exit_code -eq 0 ]; then
        test_passed=true
        echo -e "${YELLOW}   ✅ Exit code 0 - Test completed successfully${NC}"
    elif echo "$clean_output" | grep -qi -E "(continue.*shopping|payment.*screen|credit.*card)"; then
        test_passed=true
        echo -e "${YELLOW}   💳 Test reached payment flow - considering as PASSED${NC}"
    fi
    
    # Capture screenshots for failed tests
    if [ "$test_passed" = false ]; then
        echo -e "${YELLOW}   📸 Capturing failure screenshots...${NC}"
        adb exec-out screencap -p > "$screenshots_dir/failure_screenshot_$(date +%H%M%S).png" 2>/dev/null || true
    fi
    
    # Generate HTML report for this test
    generate_test_html_report "$output_dir" "$name" "$file" "$clean_output" "$test_passed" "$screenshots_dir"
    
    # Final result
    if [ "$test_passed" = true ]; then
        echo -e "${GREEN}✅ PASSED at $(date '+%H:%M:%S')${NC}"
        ((PASSED++))
        TEST_RESULTS+=("PASSED:$name:$num:Success")
    else
        echo -e "${RED}❌ FAILED at $(date '+%H:%M:%S') (continuing...)${NC}"
        ((FAILED++))
        TEST_RESULTS+=("FAILED:$name:$num:Exit code $exit_code")
    fi
    
    echo -e "${YELLOW}   📋 Report: test_${num}_${name}/index.html${NC}"
    echo ""
}

# Function to generate HTML report for individual test (same format as run_sanity_test.sh)
generate_test_html_report() {
    local output_dir="$1"
    local name="$2"
    local file="$3"
    local clean_output="$4"
    local test_passed="$5"
    local screenshots_dir="$6"
    
    local status_color="#4CAF50"
    local status_text="PASSED"
    local status_icon="✅"
    
    if [ "$test_passed" = false ]; then
        status_color="#f44336"
        status_text="FAILED"
        status_icon="❌"
    fi
    
    cat > "$output_dir/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Test Report: $name</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, $status_color 0%, $status_color 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .header h1 { margin: 0; font-size: 2em; }
        .header p { margin: 5px 0 0 0; opacity: 0.9; }
        .status { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 5px solid $status_color; margin-bottom: 20px; }
        .log { background: #f9f9f9; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #ddd; }
        .log pre { margin: 0; white-space: pre-wrap; word-wrap: break-word; font-size: 12px; }
        .screenshots { margin: 20px 0; }
        .screenshot { margin: 15px; display: inline-block; text-align: center; }
        .screenshot img { max-width: 300px; border: 2px solid #ddd; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .screenshot p { margin: 10px 0; font-weight: bold; color: #666; }
        .no-screenshots { text-align: center; color: #666; font-style: italic; padding: 20px; }
        .back-link { margin-top: 30px; text-align: center; }
        .back-link a { color: #2196F3; text-decoration: none; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>$status_icon Test Report: $name</h1>
            <p><strong>File:</strong> $file</p>
            <p><strong>Execution Time:</strong> $(date '+%Y-%m-%d %H:%M:%S')</p>
        </div>
        
        <div class="status">
            <h3>$status_icon Test Status: $status_text</h3>
            <p>This test was executed and the result is shown above. Check the execution log below for detailed information.</p>
        </div>
        
        <div class="log">
            <h3>📋 Execution Log</h3>
            <pre>$(echo "$clean_output" | sed 's/&/\&amp;/g; s/</\&lt;/g; s/>/\&gt;/g')</pre>
        </div>
        
        <div class="screenshots">
            <h3>📸 Screenshots</h3>
EOF

    # Add screenshots if any exist
    if [ -d "$screenshots_dir" ] && [ "$(ls -A "$screenshots_dir" 2>/dev/null)" ]; then
        for screenshot in "$screenshots_dir"/*.png; do
            if [ -f "$screenshot" ]; then
                local screenshot_name=$(basename "$screenshot")
                cat >> "$output_dir/index.html" << EOF
            <div class="screenshot">
                <img src="screenshots/$screenshot_name" alt="$screenshot_name" onclick="window.open(this.src)">
                <p>$screenshot_name</p>
            </div>
EOF
            fi
        done
    else
        cat >> "$output_dir/index.html" << EOF
            <div class="no-screenshots">
                <p>No screenshots were captured for this test.</p>
                <p>Screenshots are automatically captured for failed test cases.</p>
            </div>
EOF
    fi

    cat >> "$output_dir/index.html" << EOF
        </div>
        
        <div class="back-link">
            <a href="../index.html">← Back to Test Suite Summary</a>
        </div>
    </div>
</body>
</html>
EOF
}

# Pre-execution checks
echo -e "${BLUE}🔍 Pre-execution Checks:${NC}"

if [ ! -f "$TESTCASES_FILE" ]; then
    echo -e "${RED}❌ testcases.txt not found${NC}"
    exit 1
fi

if command -v maestro &> /dev/null; then
    echo -e "${GREEN}✅ Maestro found${NC}"
else
    echo -e "${RED}❌ Maestro not found${NC}"
    exit 1
fi

if adb devices | grep -q "device$"; then
    echo -e "${GREEN}✅ Device connected${NC}"
else
    echo -e "${YELLOW}⚠️  No device detected${NC}"
fi

echo -e "${GREEN}✅ Ready to execute${NC}"
echo ""

# Main execution
cleanup_reports

echo -e "${BLUE}📋 Reading test cases from ${TESTCASES_FILE}...${NC}"

# Count and display test cases
test_count=0
line_num=0
while IFS= read -r line || [ -n "$line" ]; do
    ((line_num++))
    if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# && ! "$line" =~ ^[[:space:]]*$ ]]; then
        test_region=$(get_region "$line" "$line_num")
        # When no region filter: run only international countries (TH, SA, UAE, SG), exclude IN
        # When region filter specified: run only that region or combined regions
        if [ -n "$REGION_FILTER" ]; then
            # Specific region filter provided
            if [ "$REGION_FILTER" = "$test_region" ]; then
                should_run=true
            elif [ "$REGION_FILTER" = "SG_TH" ] && ([ "$test_region" = "SG" ] || [ "$test_region" = "TH" ]); then
                should_run=true
            elif [ "$REGION_FILTER" = "UAE_SA" ] && ([ "$test_region" = "UAE" ] || [ "$test_region" = "SA" ]); then
                should_run=true
            else
                should_run=false
            fi
        else
            # No region filter: run only international countries (exclude IN)
            if [ "$test_region" = "TH" ] || [ "$test_region" = "SA" ] || [ "$test_region" = "UAE" ] || [ "$test_region" = "SG" ]; then
                should_run=true
            else
                should_run=false
            fi
        fi

        if [ "$should_run" = true ]; then
            ((test_count++))
            echo -e "${YELLOW}   ${test_count}. [$(get_region_flag "$test_region") $test_region] $(basename "$line")${NC}"
        fi
    fi
done < "$TESTCASES_FILE"

echo -e "${GREEN}✅ Found ${test_count} test cases to execute${NC}"
echo ""

if [ $test_count -eq 0 ]; then
    echo -e "${RED}❌ No test cases found matching filter${NC}"
    exit 1
fi

echo -e "${BLUE}🚀 Starting Test Execution${NC}"
echo -e "${BLUE}═══════════════════════════════════════${NC}"

# Execute tests
test_num=1
line_num=0
while IFS= read -r line || [ -n "$line" ]; do
    ((line_num++))
    if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# && ! "$line" =~ ^[[:space:]]*$ ]]; then
        test_region=$(get_region "$line" "$line_num")

        # Check if we should run this test based on region filter
        # When no region filter: run only international countries (TH, SA, UAE, SG), exclude IN
        # When region filter specified: run only that region or combined regions
        if [ -n "$REGION_FILTER" ]; then
            # Specific region filter provided
            if [ "$REGION_FILTER" = "$test_region" ]; then
                should_run=true
            elif [ "$REGION_FILTER" = "SG_TH" ] && ([ "$test_region" = "SG" ] || [ "$test_region" = "TH" ]); then
                should_run=true
            elif [ "$REGION_FILTER" = "UAE_SA" ] && ([ "$test_region" = "UAE" ] || [ "$test_region" = "SA" ]); then
                should_run=true
            else
                should_run=false
            fi
        else
            # No region filter: run only international countries (exclude IN)
            if [ "$test_region" = "TH" ] || [ "$test_region" = "SA" ] || [ "$test_region" = "UAE" ] || [ "$test_region" = "SG" ]; then
                should_run=true
            else
                should_run=false
            fi
        fi

        if [ "$should_run" = true ]; then
            run_test "$line" "$test_num" "$test_region"
            ((test_num++))
        fi
    fi
done < "$TESTCASES_FILE"

# Results summary
echo -e "${BLUE}📊 FINAL RESULTS${NC}"
echo -e "${BLUE}═══════════════════════════════════════${NC}"
echo -e "${YELLOW}Total: ${TOTAL} | Passed: ${PASSED} | Failed: ${FAILED}${NC}"

if [ $TOTAL -gt 0 ]; then
    success_rate=$((PASSED * 100 / TOTAL))
    echo -e "${YELLOW}Success Rate: ${success_rate}%${NC}"
fi

echo ""
for result in "${TEST_RESULTS[@]}"; do
    IFS=':' read -r status name num details <<< "$result"
    if [ "$status" = "PASSED" ]; then
        echo -e "${GREEN}✅ Test ${num}: ${name}${NC}"
    else
        echo -e "${RED}❌ Test ${num}: ${name}${NC}"
    fi
done

# Generate main HTML report (same format as run_sanity_test.sh)
echo ""
echo -e "${BLUE}📄 Generating HTML report...${NC}"

success_rate=0
if [ $TOTAL -gt 0 ]; then
    success_rate=$((PASSED * 100 / TOTAL))
fi

cat > "${REPORT_DIR}/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Test Results - ${DATETIME}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); color: white; padding: 25px; border-radius: 8px; margin-bottom: 20px; }
        .header h1 { margin: 0; font-size: 2.2em; }
        .header p { margin: 5px 0 0 0; opacity: 0.9; }
        .region-info { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .summary { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 5px solid #2196F3; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .stat-card h4 { margin: 0; font-size: 1.8em; }
        .stat-card.total { border-top: 3px solid #2196F3; }
        .stat-card.passed { border-top: 3px solid #4CAF50; }
        .stat-card.failed { border-top: 3px solid #f44336; }
        .test-list { margin: 20px 0; }
        .test-item { padding: 15px; margin: 8px 0; border-radius: 8px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .test-item a { text-decoration: none; color: #1976D2; font-weight: 500; }
        .test-item.passed { border-left: 5px solid #4CAF50; }
        .test-item.failed { border-left: 5px solid #f44336; }
        .footer { margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Sanity Test Results</h1>
            <p><strong>📅 Execution Time:</strong> ${DATETIME}</p>
            <p><strong>📋 Source:</strong> ${TESTCASES_FILE}</p>
EOF

if [ -n "$REGION_FILTER" ]; then
    cat >> "${REPORT_DIR}/index.html" << EOF
            <p><strong>🌍 Region Filter:</strong> ${REGION_FILTER}</p>
EOF
fi

cat >> "${REPORT_DIR}/index.html" << EOF
        </div>

        <div class="region-info">
            <h3>🌍 Region Information</h3>
EOF

# Add region-specific information based on filter
if [ -n "$REGION_FILTER" ]; then
    if [ "$REGION_FILTER" = "IN" ]; then
        cat >> "${REPORT_DIR}/index.html" << EOF
            <p><strong>🇮🇳 IN (India):</strong> Tests for Indian market with INR currency and India-specific features</p>
EOF
    elif [ "$REGION_FILTER" = "SG" ]; then
        cat >> "${REPORT_DIR}/index.html" << EOF
            <p><strong>🇸🇬 SG (Singapore):</strong> Tests for Singapore market with SGD currency and Singapore-specific features</p>
EOF
    elif [ "$REGION_FILTER" = "UAE" ]; then
        cat >> "${REPORT_DIR}/index.html" << EOF
            <p><strong>🇦🇪 UAE (United Arab Emirates):</strong> Tests for UAE market with AED currency and UAE-specific features</p>
EOF
    elif [ "$REGION_FILTER" = "SA" ]; then
        cat >> "${REPORT_DIR}/index.html" << EOF
            <p><strong>🇸🇦 SA (Saudi Arabia):</strong> Tests for Saudi Arabia market with SAR currency and Saudi-specific features</p>
EOF
    elif [ "$REGION_FILTER" = "TH" ]; then
        cat >> "${REPORT_DIR}/index.html" << EOF
            <p><strong>🇹🇭 TH (Thailand):</strong> Tests for Thailand market with THB currency and Thailand-specific features</p>
EOF
    elif [ "$REGION_FILTER" = "SG_TH" ]; then
        cat >> "${REPORT_DIR}/index.html" << EOF
            <p><strong>🇸🇬 SG (Singapore):</strong> Tests for Singapore market with SGD currency and Singapore-specific features</p>
            <p><strong>🇹🇭 TH (Thailand):</strong> Tests for Thailand market with THB currency and Thailand-specific features</p>
            <p><em>Combined execution: Singapore & Thailand markets</em></p>
EOF
    elif [ "$REGION_FILTER" = "UAE_SA" ]; then
        cat >> "${REPORT_DIR}/index.html" << EOF
            <p><strong>🇦🇪 UAE (United Arab Emirates):</strong> Tests for UAE market with AED currency and UAE-specific features</p>
            <p><strong>🇸🇦 SA (Saudi Arabia):</strong> Tests for Saudi Arabia market with SAR currency and Saudi-specific features</p>
            <p><em>Combined execution: UAE & Saudi Arabia markets</em></p>
EOF
    fi
else
    # Show only international regions when no filter is applied (exclude IN)
    cat >> "${REPORT_DIR}/index.html" << EOF
            <p><strong>🇸🇬 SG (Singapore):</strong> Tests for Singapore market with SGD currency and Singapore-specific features</p>
            <p><strong>🇦🇪 UAE (United Arab Emirates):</strong> Tests for UAE market with AED currency and UAE-specific features</p>
            <p><strong>🇸🇦 SA (Saudi Arabia):</strong> Tests for Saudi Arabia market with SAR currency and Saudi-specific features</p>
            <p><strong>🇹🇭 TH (Thailand):</strong> Tests for Thailand market with THB currency and Thailand-specific features</p>
            <p><em>Note: Running all international markets (IN excluded)</em></p>
EOF
fi

cat >> "${REPORT_DIR}/index.html" << EOF
        </div>

        <div class="summary">
            <h3>📊 Execution Summary</h3>
            <div class="stats">
                <div class="stat-card total">
                    <h4>${TOTAL}</h4>
                    <p>Total Tests</p>
                </div>
                <div class="stat-card passed">
                    <h4>${PASSED}</h4>
                    <p>Passed</p>
                </div>
                <div class="stat-card failed">
                    <h4>${FAILED}</h4>
                    <p>Failed</p>
                </div>
                <div class="stat-card">
                    <h4>${success_rate}%</h4>
                    <p>Success Rate</p>
                </div>
            </div>
        </div>



        <div class="test-list">
            <h3>📋 Test Reports</h3>
EOF

# Add test results
for result in "${TEST_RESULTS[@]}"; do
    IFS=':' read -r status name num details <<< "$result"

    if [ "$status" = "PASSED" ]; then
        cat >> "${REPORT_DIR}/index.html" << EOF
            <div class="test-item passed">
                <a href="test_${num}_${name}/index.html">✅ Test ${num}: ${name}</a>
                <br><small>📊 Status: Passed | 📸 Screenshots available</small>
            </div>
EOF
    else
        cat >> "${REPORT_DIR}/index.html" << EOF
            <div class="test-item failed">
                <a href="test_${num}_${name}/index.html">❌ Test ${num}: ${name}</a>
                <br><small>📊 Status: Failed | 📸 Failure screenshots captured</small>
            </div>
EOF
    fi
done

cat >> "${REPORT_DIR}/index.html" << EOF
        </div>

        <div class="footer">
            <p><strong>🔍 Debug Info:</strong> Check individual test reports for detailed screenshots and logs</p>
            <p><strong>🔄 Guaranteed Execution:</strong> All tests run regardless of individual failures</p>
        </div>
    </div>
</body>
</html>
EOF

echo -e "${GREEN}✅ HTML report generated${NC}"

echo ""
echo -e "${BLUE}📁 Report: ${REPORT_DIR}/index.html${NC}"
echo -e "${BLUE}🌐 Opening report...${NC}"
open "${REPORT_DIR}/index.html" 2>/dev/null || true

echo ""
echo -e "${GREEN}🎉 Test execution completed!${NC}"
echo -e "${YELLOW}📊 ${PASSED}/${TOTAL} tests passed${NC}"

# Send email notification with test results if sendMail=true
if [ "$SEND_MAIL" = true ]; then
    echo ""
    echo -e "${BLUE}📧 Sending email notification...${NC}"

    # Check if Python is available
    if command -v python3 &> /dev/null; then
        # Send email with test results
        if [ -n "$REGION_FILTER" ]; then
            python3 ./send_email.py --region "$REGION_FILTER" --reports-dir "$REPORT_DIR" --test-type "sanity"
        else
            python3 ./send_email.py --reports-dir "$REPORT_DIR" --test-type "sanity"
        fi

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Email notification sent successfully${NC}"
        else
            echo -e "${RED}❌ Failed to send email notification${NC}"
            echo -e "${YELLOW}⚠️ Make sure to set the GMAIL_APP_PASSWORD environment variable:${NC}"
            echo -e "${YELLOW}   export GMAIL_APP_PASSWORD='your_app_password'${NC}"
        fi
    else
        echo -e "${RED}❌ Python 3 not found - email notification skipped${NC}"
        echo -e "${YELLOW}⚠️ Install Python 3 to enable email notifications${NC}"
    fi
else
    echo ""
    echo -e "${YELLOW}📧 Email notification skipped (use sendMail=true to enable)${NC}"
    echo -e "${YELLOW}   Example: ./sanity.sh ${REGION_FILTER:+$REGION_FILTER }sendMail=true${NC}"
fi
