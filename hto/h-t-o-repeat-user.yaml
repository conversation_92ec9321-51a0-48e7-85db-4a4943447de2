appId: com.lenskart.app

env:
  PINCODE: "846003"
  HOUSENO: "Test Test"
  LANDMARK: "Test"

---
- clearState
- runFlow: ../login/Popup.yaml

- tapOn: "Try At Home"

#Verify user lands on HTO Page successfully
- assertVisible:
    "Lenskart at Home"

- tapOn: "Change"
- tapOn: "Add Address"
- tapOn: "Change"

- tapOn:
    id: "com.lenskart.app:id/edtManualSearch"

- inputText:
    text: ${PINCODE}

- tapOn:
    id: "android:id/text1"
    index: 0

- tapOn: "Confirm Appointment Location"

- tapOn:
    id: "com.lenskart.app:id/edtHouseAndFlat"

- inputText:
    text: ${HOUSENO}

- tapOn:
    id: "com.lenskart.app:id/edtRoadAreaLandmark"

- inputText:
    text: ${LANDMARK}

- tapOn:
    id: "com.lenskart.app:id/button_container"

#Verify pincode is unservicable
- assertVisible:
    "No slots available at your location.Try with another location"

- tapOn: "Change"
- tapOn:
    id: "com.lenskart.app.store:id/main_container"
    index: 0

- tapOn: "Proceed to date & time"

#Verify button and banner  visible on slot screen

- assertVisible:
    id: "com.lenskart.app:id/iv_logo_2"
- assertVisible:
        id: "com.lenskart.app:id/button_container"

- tapOn:
    id: "com.lenskart.app:id/layout_date"
    index: 0

#Verify amount for 1 user
- assertVisible:
    "₹0"

- tapOn:
        id: "com.lenskart.app:id/layout_date"
        index: 1

#Verify amount for 2 users
- assertVisible:
        "₹0"
- tapOn:
    id: "com.lenskart.app:id/button_container"

#Verify order placed successfully
- assertVisible:
     "Home Eye Checkup Scheduled"


