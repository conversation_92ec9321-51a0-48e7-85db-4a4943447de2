appId: com.lenskart.app

env:
  PINCODE: "122002"
  HOUSENO: "Test Test"
  LANDMARK: "Test"

---
- clearState
- runFlow: ../login/Login-hto-new-user.yaml

- tapOn: "Try At Home"

#Verify user lands on HTO Page successfully
- assertVisible:
    "Eye Test at Home"

- tapOn: "Book Appointment"

- tapOn: "Proceed to date & time"

#Verify button visible on slot screen
- assertVisible:
    id: "com.lenskart.app:id/button_container"

- tapOn:
    id: "com.lenskart.app:id/layout_date"
    index: 0

#Verify amount for 1 user
- assertVisible:
    "₹99"

- tapOn:
    id: "com.lenskart.app:id/layout_date"
    index: 1

#Verify amount for 2 users
- assertVisible:
    "₹198"

