appId: com.lenskart.app

---
- launchApp
- waitForAnimationToEnd
-
#tap on go to home
- tapOn:
    text: "Go to home"
    optional: true

- runFlow: ../../../pdp/sg-pdp/sg-progressive-pdp.yaml

- runFlow: ../../../pdp/sg-package-screen/sg-eyeglass-progressive.yaml

- runFlow:
    file: ../../../progressive/optimized-progressive.yaml
    env:
      lensType: "Tokai Progressive"

- tapOn: "Skip coating addition"

- runFlow: ../../../cart/sg-cart/sg-progressive-product-cart.yaml

- runFlow: ../../../address/sg-studioflow.yaml

- runFlow: ../../../payment/sg-grabpay-payment.yaml
