appId: com.lenskart.app
env:
  PID: 213008

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

#tapping on Search icon (try multiple approaches)
- tapOn:
    id: "com.lenskart.app:id/iv_icon"
    index: 0

#entering the PID
- inputText: ${PID}

#tap on product from result
- tapOn:
    id: "com.lenskart.app:id/ivTrailingIcon"

# Run PDP section flows one by one
- runFlow: ../../../pdp/gallery-view.yaml

#ratings and review
- runFlow: ../../../pdp/th-ratings-review.yaml

#verifying frame color
- runFlow: ../../../pdp/frame-color.yaml

#verifying frame size
- runFlow: ../../../pdp/th-frame-size.yaml

#scrolling down till ratings and reviwes
- scrollUntilVisible:
    element:
      text: "Check Delivery & Services"
    direction: DOWN

- runFlow: ../../../pdp/international-deivery-details.yaml
- runFlow: ../../../pdp/international-similar-products.yaml

