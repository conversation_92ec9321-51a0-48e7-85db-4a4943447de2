#!/bin/bash

# Enhanced Maestro TCP Connection Fix Script
# Permanent solution for: "Command failed (tcp:7001): closed"

echo "🔧 Enhanced Maestro TCP Connection Fix..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Step 1: Kill all related processes
echo "1️⃣  Killing all Maestro and ADB processes..."
pkill -f maestro
pkill -f adb
sleep 2

# Step 2: Force kill any remaining processes on port 7001
echo "2️⃣  Clearing port 7001..."
if lsof -ti:7001 > /dev/null 2>&1; then
    echo "   Killing processes on port 7001..."
    lsof -ti:7001 | xargs kill -9 2>/dev/null || true
fi
sleep 1

# Step 3: Reset ADB completely
echo "3️⃣  Resetting ADB server..."
adb kill-server
sleep 3
adb start-server
sleep 2

# Step 4: Verify device connection
echo "4️⃣  Verifying device connection..."
DEVICE_COUNT=$(adb devices | grep -c "device$")
if [ "$DEVICE_COUNT" -gt 0 ]; then
    echo "✅ Device(s) connected:"
    adb devices
else
    echo "❌ No devices found. Please check USB connection."
    exit 1
fi

# Step 5: Test Maestro connectivity
echo "5️⃣  Testing Maestro connectivity..."
if command -v maestro > /dev/null 2>&1; then
    echo "✅ Maestro is available"
else
    echo "❌ Maestro not found in PATH"
    exit 1
fi

# Step 6: Final port check
echo "6️⃣  Final port verification..."
if lsof -i :7001 > /dev/null 2>&1; then
    echo "⚠️  Port 7001 still in use:"
    lsof -i :7001
else
    echo "✅ Port 7001 is free"
fi

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎉 Enhanced fix complete! Ready for testing."
echo ""
echo "💡 Usage: ./fix-maestro-connection.sh"
echo "🔄 Run this script whenever you see TCP errors."
