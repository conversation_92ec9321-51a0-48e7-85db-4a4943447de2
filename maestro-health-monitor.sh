#!/bin/bash

# Maestro Health Monitor
# Prevents TCP connection issues by monitoring and maintaining healthy connections

LOG_FILE="maestro-health.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

check_adb_connection() {
    local device_count=$(adb devices | grep -c "device$")
    if [ "$device_count" -eq 0 ]; then
        log_message "WARNING: No devices connected"
        return 1
    else
        log_message "INFO: $device_count device(s) connected"
        return 0
    fi
}

check_port_7001() {
    if lsof -i :7001 > /dev/null 2>&1; then
        log_message "WARNING: Port 7001 is in use"
        lsof -i :7001 | tee -a "$LOG_FILE"
        return 1
    else
        log_message "INFO: Port 7001 is free"
        return 0
    fi
}

check_maestro_processes() {
    local maestro_count=$(ps aux | grep -c "[m]aestro")
    if [ "$maestro_count" -gt 3 ]; then
        log_message "WARNING: Too many Maestro processes ($maestro_count)"
        return 1
    else
        log_message "INFO: Normal Maestro process count ($maestro_count)"
        return 0
    fi
}

auto_fix() {
    log_message "FIXING: Running automatic fix..."
    pkill -f maestro
    pkill -f adb
    adb kill-server
    sleep 3
    adb start-server
    sleep 2
    
    if check_adb_connection && check_port_7001; then
        log_message "SUCCESS: Auto-fix completed successfully"
        return 0
    else
        log_message "ERROR: Auto-fix failed"
        return 1
    fi
}

# Main monitoring function
monitor_health() {
    log_message "Starting health check..."
    
    local issues=0
    
    if ! check_adb_connection; then
        ((issues++))
    fi
    
    if ! check_port_7001; then
        ((issues++))
    fi
    
    if ! check_maestro_processes; then
        ((issues++))
    fi
    
    if [ "$issues" -gt 0 ]; then
        log_message "DETECTED: $issues issue(s) found"
        if [ "$1" = "--auto-fix" ]; then
            auto_fix
        else
            log_message "SUGGESTION: Run with --auto-fix to automatically resolve issues"
        fi
    else
        log_message "SUCCESS: All systems healthy"
    fi
}

# Usage information
show_usage() {
    echo "Maestro Health Monitor"
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --monitor     Run health check once"
    echo "  --auto-fix    Run health check and auto-fix issues"
    echo "  --watch       Continuous monitoring (every 30 seconds)"
    echo "  --log         Show recent log entries"
    echo "  --help        Show this help message"
}

# Main script logic
case "$1" in
    --monitor)
        monitor_health
        ;;
    --auto-fix)
        monitor_health --auto-fix
        ;;
    --watch)
        log_message "Starting continuous monitoring..."
        while true; do
            monitor_health --auto-fix
            sleep 30
        done
        ;;
    --log)
        if [ -f "$LOG_FILE" ]; then
            tail -20 "$LOG_FILE"
        else
            echo "No log file found"
        fi
        ;;
    --help|*)
        show_usage
        ;;
esac
