#!/bin/bash

# Permanent Setup for Maestro TCP Error Prevention
# Run this once to set up permanent solutions

echo "🚀 Setting up permanent Maestro TCP error prevention..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Detect shell type
if [ -n "$ZSH_VERSION" ]; then
    SHELL_RC="$HOME/.zshrc"
    SHELL_NAME="zsh"
elif [ -n "$BASH_VERSION" ]; then
    SHELL_RC="$HOME/.bash_profile"
    SHELL_NAME="bash"
else
    SHELL_RC="$HOME/.profile"
    SHELL_NAME="shell"
fi

echo "1️⃣  Detected $SHELL_NAME shell, using $SHELL_RC"

# Add aliases to shell configuration
echo "2️⃣  Adding Maestro aliases to $SHELL_RC..."

# Check if aliases already exist
if grep -q "# Maestro Automation Aliases" "$SHELL_RC" 2>/dev/null; then
    echo "   ⚠️  Aliases already exist in $SHELL_RC"
else
    echo "" >> "$SHELL_RC"
    echo "# Maestro Automation Aliases - Auto-added by setup script" >> "$SHELL_RC"
    echo "alias fix-maestro='pkill -f maestro && pkill -f adb && adb kill-server && sleep 3 && adb start-server && adb devices'" >> "$SHELL_RC"
    echo "alias check-device='adb devices'" >> "$SHELL_RC"
    echo "alias check-port='lsof -i :7001'" >> "$SHELL_RC"
    echo "alias reset-automation='pkill -f maestro && pkill -f adb && adb kill-server && sleep 3 && adb start-server && adb devices'" >> "$SHELL_RC"
    echo "   ✅ Aliases added successfully"
fi

# Create daily maintenance cron job
echo "3️⃣  Setting up daily ADB maintenance..."
CRON_JOB="0 9 * * * cd $(pwd) && ./maestro-health-monitor.sh --auto-fix >> maestro-health.log 2>&1"

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "maestro-health-monitor"; then
    echo "   ⚠️  Cron job already exists"
else
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    echo "   ✅ Daily maintenance scheduled for 9 AM"
fi

# Create system limits configuration
echo "4️⃣  Optimizing system limits..."
if [ "$(uname)" = "Darwin" ]; then
    # macOS specific optimizations
    echo "   📱 macOS detected - applying optimizations..."
    
    # Increase file descriptor limits
    if ! grep -q "maestro-optimization" /etc/launchd.conf 2>/dev/null; then
        echo "limit maxfiles 65536 200000 # maestro-optimization" | sudo tee -a /etc/launchd.conf > /dev/null
        echo "   ✅ File descriptor limits increased"
    fi
fi

# Test the setup
echo "5️⃣  Testing the setup..."
source "$SHELL_RC" 2>/dev/null || true

if command -v fix-maestro > /dev/null 2>&1; then
    echo "   ✅ Aliases loaded successfully"
else
    echo "   ⚠️  Aliases not loaded - restart terminal or run: source $SHELL_RC"
fi

# Create quick reference card
echo "6️⃣  Creating quick reference..."
cat > maestro-quick-reference.txt << 'EOF'
# Maestro TCP Error - Quick Reference

## Immediate Fix Commands:
fix-maestro                    # Quick fix alias
./fix-maestro-connection.sh    # Enhanced fix script
reset-automation               # Complete reset alias

## Monitoring Commands:
check-device                   # Check connected devices
check-port                     # Check port 7001 usage
./maestro-health-monitor.sh --monitor  # Health check

## Prevention:
./maestro-health-monitor.sh --watch    # Continuous monitoring
./maestro-health-monitor.sh --auto-fix # Auto-fix issues

## Daily Maintenance:
- Cron job runs daily at 9 AM
- Automatically fixes common issues
- Logs all activities

## Manual Commands:
pkill -f maestro && pkill -f adb && adb kill-server && sleep 3 && adb start-server && adb devices
EOF

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎉 Permanent setup complete!"
echo ""
echo "📋 What was installed:"
echo "   ✅ Shell aliases for quick fixes"
echo "   ✅ Daily maintenance cron job"
echo "   ✅ System optimizations"
echo "   ✅ Health monitoring scripts"
echo "   ✅ Quick reference guide"
echo ""
echo "🔄 Next steps:"
echo "   1. Restart your terminal or run: source $SHELL_RC"
echo "   2. Test with: fix-maestro"
echo "   3. Read: maestro-quick-reference.txt"
echo ""
echo "💡 Available commands:"
echo "   fix-maestro, check-device, check-port, reset-automation"
