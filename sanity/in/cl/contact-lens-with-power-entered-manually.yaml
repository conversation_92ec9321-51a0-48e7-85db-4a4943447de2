appId: com.lenskart.app

---
- runFlow: ../../../login/popup.yaml

- runFlow:
    file: ../../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: Contact Lenses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Contact Lenses
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

- tapOn: "Add to Cart"

- runFlow: ../../../clPowerType/c-l-enter-power-manually.yaml

- tapOn: "Add to Cart"

- runFlow:
    when:
      visible: 'Skip to cart'
    commands:
      - tapOn: "Skip to cart"

- tapOn:
    id: "com.lenskart.app:id/btn_continue"

- runFlow: ../../../address/saved-address.yaml

- runFlow: ../../../payment/credit-card-details.yaml

