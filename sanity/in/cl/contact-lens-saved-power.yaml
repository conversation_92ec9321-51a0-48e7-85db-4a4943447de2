appId: com.lenskart.app

env:
  SEARCHKEYWORD: "Contact Lenses"

---
# Step 1: Login to the app
- runFlow: ../../../login/popup.yaml

- runFlow:
    file: ../../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: "Contact Lenses"

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

- tapOn: "Add to Cart"

- runFlow: ../../../clPowerType/c-l-used-saved-power.yaml

- tapOn:
    text: "Add to Cart"
    optional: true


# Step 7: Handle skip to cart option
- runFlow:
    when:
      visible: 'Skip to cart'
    commands:
      - tapOn: "Skip to cart"

# Wait for address screen


# Step 8: Select address
- runFlow: ../../../address/saved-address.yaml

# Wait for payment screen


# Step 9: Process payment (will cancel at the end)
- runFlow: ../../../payment/credit-card-details.yaml

