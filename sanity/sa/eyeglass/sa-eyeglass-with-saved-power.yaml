appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
      text: "Go to home"
      optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- runFlow: ../../../home/<USER>

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

- tapOn: "Add to Cart"

- runFlow:
    file: ../../../power/optimized-eyeglass-with-power.yaml
    env:
      lensType: "Anti-Glare Premium"
- runFlow: ../../../addPower/international-saved-powers.yaml
- tapOn: "Select Address"
- runFlow:
    when:
      visible: "Submit Eye Power"
    file: ../../../clPowerType/uae-cl-submit-power-later.yaml
- runFlow: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/sa-tamara-payment.yaml