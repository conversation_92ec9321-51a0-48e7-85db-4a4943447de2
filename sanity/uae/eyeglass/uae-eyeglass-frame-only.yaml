appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#- runFlow:
  #  file: ../../../search/international-search-keyword.yaml
   # env:
    #  SEARCHKEYWORD : Eyeglass # <-- Run commands from "search keyword.yaml"
    #  label: Search keywrod passed as Eyeglass

#- tapOn:
   # text: "Eyeglasses"
  #  index: 1

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#- runFlow:
   #   file: ../../../search/international-search-keyword.yaml
    #  env:
        #  SEARCHKEYWORD: 230543 # <-- Run commands from "searchkeyword.yaml"
       #   label: Search keywrod passed as PID

#click on product
#- tapOn:
  #  id: "com.lenskart.app:id/ivTrailingIcon"

- tapOn: "Classic"

#select eyeglass
- tapOn:
     id: "com.lenskart.app:id/image"
     index: 1

- runFlow: ../../../plp/uae-plp.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- tapOn:
    text: "Frame Only"
    optional: true
# Wait for any animations to complete
- waitForAnimationToEnd
# Handle address selection with better error handling
- tapOn: "Select Address"
- runFlow:
    file: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/international-c-c-details.yaml