appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#- runFlow:
 #   file: ../../../search/international-search-keyword.yaml
#    env:
  #    SEARCHKEYWORD : lens spherical # <-- Run commands from "searchkeyword.yaml"
   #   label: Search keywrod passed as lens spherical

# tapOn:
    #id: "com.lenskart.app:id/query"
- tapOn: "Premium"
- tapOn: "Contact Lens"

#select cl
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#select cl category from bottomsheet
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
- runFlow: ../../../plp/sa-plp.yaml
- scrollUntilVisible:
    element:
      text:  "I will submit power later"
    direction: DOWN

#Tap on Submit Power Later
- tapOn:
    id: "com.lenskart.app:id/rb_ask_me_later"

#tap on add to cart
- tapOn: "Add to Cart"

- tapOn: "Select Address"
- runFlow:
    when:
      visible: "Submit Eye Power"
    file: ../../../clPowerType/uae-cl-submit-power-later.yaml
- tapOn: "Select Address"
- runFlow:
      when:
          visible: "Submit Eye Power"
      file: ../../../clPowerType/uae-cl-submit-power-later.yaml
- runFlow: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/international-c-c-details.yaml


