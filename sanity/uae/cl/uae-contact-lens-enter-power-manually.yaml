appId: com.lenskart.app

---
# Step 1: Login to the app with Singapore country
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: United Arab Emirates

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#- runFlow: ../../../login/international-login.yaml

#- runFlow:
 #   file: ../../../search/international-search-keyword.yaml
   # env:
    #  SEARCHKEYWORD : lens spherical # <-- Run commands from "searchkeyword.yaml"
   #   label: Search keywrod passed as lens spherical


#tap on arrow
#- tapOn:
 #   id: "com.lenskart.app:id/ivTrailingIcon"

- tapOn: "Premium"
- tapOn: "Contact Lens"
#select cl
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#select cl category from bottomsheet
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
- runFlow: ../../../plp/sa-plp.yaml
- scrollUntilVisible:
    element:
      text:  "I will submit power later"
    direction: DOWN
- tapOn: "Enter Power Manually"
- runFlow: ../../../clPowerType/international-addl-Cl-powers.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- tapOn: "Select Address"
- runFlow:
    when:
      visible: "Submit Eye Power"
    file: ../../../clPowerType/uae-cl-submit-power-later.yaml
- runFlow: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/international-c-c-details.yaml
