appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Sunglasses

#clicking on eyeglasses text
- tapOn:
    id: "com.lenskart.app:id/query"
    index: 0

#select first product on plp
- runFlow: ../../../plp/international-plp.yaml

- runFlow: ../../../pdp/sg-package-screen/sg-Sunglass-with-power.yaml

- runFlow:
    file: ../../../power/optimized-eyeglass-with-power.yaml
    env:
      lensType: "Blue Tinted"

- runFlow: ../../../addPower/add-power-saved-powers.yaml

- runFlow: ../../../cart/sg-cart/sg-regular-product-cart.yaml

- runFlow: ../../../address/sg-saved-address.yaml

- runFlow: ../../../payment/sg-atome-payment.yaml

