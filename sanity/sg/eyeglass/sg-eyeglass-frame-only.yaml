appId: com.lenskart.app

env:
  MOBILENO: 2123000009
  OTP: 7777
  SEARCHKEYWORD: "Eyeglass"

---

# Step 1: Login to the app with Singapore country
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: Singapore


# Step 3: Search for eyeglass
- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: "Eyeglass"

- pressKey: Enter

# Wait for search results
- waitForAnimationToEnd

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

- tapOn:
    text: "Select Lenses"
    optional: true

# Wait for lens options


# Step 7: Choose Frame Only option
- tapOn:
    text: "Frame Only"
    optional: true



# Step 8: Handle address selection
- tapOn:
    text: "Select Address"
    optional: true


# Step 9: Use saved address
- runFlow: ../../../address/saved-address.yaml

# Step 10: Process payment
- runFlow: ../../../payment/international-c-c-details.yaml

