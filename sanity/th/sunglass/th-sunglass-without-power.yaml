appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true
# Run login flow first
#- runFlow: ../../../login/optimized-login.yaml

#- runFlow: ../../../login/handle-permissions.yaml

#Search keyword by using search icon
- runFlow: ../../../search/international-search-icon.yaml

#Search keyword
- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: sunglass # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Sunglass
- pressKey: Enter


- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

#add to cart
- tapOn: "Add to Cart"

#address
- runFlow: ../../../address/th-saved-address.yaml

#payment
- runFlow: ../../../payment/th-promptpay-payment.yaml



