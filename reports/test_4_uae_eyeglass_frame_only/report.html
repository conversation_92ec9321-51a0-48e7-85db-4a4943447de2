<html>
  <head>
    <title>Maestro Test Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  </head>
  <body>
    <div class="card mb-4">
      <div class="card-body">
        <h1 class="mt-5 text-center">Flow Execution Summary</h1>
<br>Test Result: FAILED<br>Duration: 2m 31s<br><br>
        <div class="card-group mb-4">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title text-center">Total number of Flows</h5>
              <h3 class="card-text text-center">1</h3>
            </div>
          </div>
          <div class="card text-white bg-danger">
            <div class="card-body">
              <h5 class="card-title text-center">Failed Flows</h5>
              <h3 class="card-text text-center">1</h3>
            </div>
          </div>
          <div class="card text-white bg-success">
            <div class="card-body">
              <h5 class="card-title text-center">Successful Flows</h5>
              <h3 class="card-text text-center">0</h3>
            </div>
          </div>
        </div>
        <div class="card border-danger mb-3">
          <div class="card-body text-danger"><b>Failed Flow</b><br>
            <p class="card-text">uae-eyeglass-frame-only<br></p>
          </div>
        </div>
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0"><button class="btn btn-danger" type="button" data-bs-toggle="collapse" data-bs-target="#uae-eyeglass-frame-only" aria-expanded="false" aria-controls="uae-eyeglass-frame-only">uae-eyeglass-frame-only : ERROR</button></h5>
          </div>
          <div class="collapse" id="uae-eyeglass-frame-only">
            <div class="card-body">
              <p class="card-text">Status: ERROR<br>Duration: 2m 31s<br>File Name: uae-eyeglass-frame-only</p>
              <p class="card-text text-danger">Element not found: Text matching regex: Cancel payment</p>
            </div>
          </div>
        </div>
      </div>
      <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"></script>
    </div>
  </body>
</html>
