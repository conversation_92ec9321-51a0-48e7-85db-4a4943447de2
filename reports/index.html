<!DOCTYPE html>
<html>
<head>
    <title>Test Results - 2025-07-31 13:15:32</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); color: white; padding: 25px; border-radius: 8px; margin-bottom: 20px; }
        .header h1 { margin: 0; font-size: 2.2em; }
        .header p { margin: 5px 0 0 0; opacity: 0.9; }
        .region-info { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .summary { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 5px solid #2196F3; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .stat-card h4 { margin: 0; font-size: 1.8em; }
        .stat-card.total { border-top: 3px solid #2196F3; }
        .stat-card.passed { border-top: 3px solid #4CAF50; }
        .stat-card.failed { border-top: 3px solid #f44336; }
        .test-list { margin: 20px 0; }
        .test-item { padding: 15px; margin: 8px 0; border-radius: 8px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .test-item a { text-decoration: none; color: #1976D2; font-weight: 500; }
        .test-item.passed { border-left: 5px solid #4CAF50; }
        .test-item.failed { border-left: 5px solid #f44336; }
        .footer { margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Sanity Test Results</h1>
            <p><strong>📅 Execution Time:</strong> 2025-07-31 13:15:32</p>
            <p><strong>📋 Source:</strong> sanity/testcases.txt</p>
            <p><strong>🌍 Region Filter:</strong> UAE</p>
        </div>

        <div class="region-info">
            <h3>🌍 Region Information</h3>
            <p><strong>🇦🇪 UAE (United Arab Emirates):</strong> Tests for UAE market with AED currency and UAE-specific features</p>
        </div>

        <div class="summary">
            <h3>📊 Execution Summary</h3>
            <div class="stats">
                <div class="stat-card total">
                    <h4>7</h4>
                    <p>Total Tests</p>
                </div>
                <div class="stat-card passed">
                    <h4>1</h4>
                    <p>Passed</p>
                </div>
                <div class="stat-card failed">
                    <h4>6</h4>
                    <p>Failed</p>
                </div>
                <div class="stat-card">
                    <h4>14%</h4>
                    <p>Success Rate</p>
                </div>
            </div>
        </div>



        <div class="test-list">
            <h3>📋 Test Reports</h3>
            <div class="test-item passed">
                <a href="test_1_uae_vpn/index.html">✅ Test 1: uae_vpn</a>
                <br><small>📊 Status: Passed | 📸 Screenshots available</small>
            </div>
            <div class="test-item failed">
                <a href="test_2_uae_contact_lens_enter_power_manually/index.html">❌ Test 2: uae_contact_lens_enter_power_manually</a>
                <br><small>📊 Status: Failed | 📸 Failure screenshots captured</small>
            </div>
            <div class="test-item failed">
                <a href="test_3_uae_contact_lens_i_will_submit_later/index.html">❌ Test 3: uae_contact_lens_i_will_submit_later</a>
                <br><small>📊 Status: Failed | 📸 Failure screenshots captured</small>
            </div>
            <div class="test-item failed">
                <a href="test_4_uae_eyeglass_frame_only/index.html">❌ Test 4: uae_eyeglass_frame_only</a>
                <br><small>📊 Status: Failed | 📸 Failure screenshots captured</small>
            </div>
            <div class="test-item failed">
                <a href="test_5_uae_eyeglass_with_saved_power/index.html">❌ Test 5: uae_eyeglass_with_saved_power</a>
                <br><small>📊 Status: Failed | 📸 Failure screenshots captured</small>
            </div>
            <div class="test-item failed">
                <a href="test_6_uae_sunglass_with_power/index.html">❌ Test 6: uae_sunglass_with_power</a>
                <br><small>📊 Status: Failed | 📸 Failure screenshots captured</small>
            </div>
            <div class="test-item failed">
                <a href="test_7_uae_sunglass_without_power/index.html">❌ Test 7: uae_sunglass_without_power</a>
                <br><small>📊 Status: Failed | 📸 Failure screenshots captured</small>
            </div>
        </div>

        <div class="footer">
            <p><strong>🔍 Debug Info:</strong> Check individual test reports for detailed screenshots and logs</p>
            <p><strong>🔄 Guaranteed Execution:</strong> All tests run regardless of individual failures</p>
        </div>
    </div>
</body>
</html>
